# 业务按钮点击事件使用指南

## 概述

WebSDK现在支持监听"打招呼"界面中"全部业务"区域的业务按钮点击事件。当用户点击任何业务按钮时，SDK会通过EventBus发送JSON-RPC格式的事件通知，外部程序可以监听这些事件来响应用户的业务选择。

## 功能特性

- ✅ **事件发送机制**：按钮点击时自动发送JSON-RPC格式的clientUI事件
- ✅ **按钮ID映射**：每个业务按钮都有对应的唯一ID
- ✅ **外部监听支持**：提供简单的API供外部程序监听事件
- ✅ **标准格式**：遵循JSON-RPC 2.0规范
- ✅ **类型安全**：完整的TypeScript类型支持

## 业务按钮ID映射

| 业务名称 | 按钮ID | 描述 |
|---------|--------|------|
| 账户管理 | `AccountManagement` | 开卡、挂失、密码管理、账户升降级 |
| 查询业务 | `AccountInquiry` | 账户查询、交易明细、积分服务 |
| 业务签约 | `BusinessContract` | 电子渠道签约、银信通、手机支付 |
| 转账汇款 | `Transfer` | 转账、跨行转账、人行通 |
| 储蓄存款 | `SavingsBalance` | 幸福存、定期存款、大额存单 |
| 投资理财 | `InvestmentFinancialManagement` | 理财产品、基金、风险测评 |
| 政务民生 | `ConvenienceServices` | 社保服务、水电气费、通讯费 |
| 信用卡业务 | `CreditCardBusiness` | 额度查询、账单查询、自扣还款 |
| 现金业务 | `Cash` | 大额存款、小额存款、取款 |

## 使用方法

### 1. 基础用法

```javascript
// 初始化SDK
const sdk = await WebSDK.init({
  hksttUrl: 'ws://localhost:8001',
  aiServerUrl: 'http://localhost:8000',
  debug: true
});

// 监听业务按钮点击事件
sdk.onBusinessButtonClick((jsonRpcRequest) => {
  console.log('用户点击了业务按钮:', jsonRpcRequest);

  const action = jsonRpcRequest.params.action;

  // 根据不同的业务类型执行相应操作
  switch (action) {
    case 'AccountManagement':
      console.log('用户选择了账户管理业务');
      // 处理账户管理相关逻辑
      break;
    case 'Transfer':
      console.log('用户选择了转账汇款业务');
      // 处理转账汇款相关逻辑
      break;
    // ... 其他业务类型
    default:
      console.log('未知的业务类型:', action);
  }
});

// 显示打招呼页面
sdk.showGreetingPage();
```

### 2. 高级用法 - 多个监听器

```javascript
// 可以注册多个监听器
sdk.onBusinessButtonClick((jsonRpcRequest) => {
  // 第一个监听器：记录用户行为
  const action = jsonRpcRequest.params.action;
  analytics.track('business_button_click', { action, requestId: jsonRpcRequest.id });
});

sdk.onBusinessButtonClick((jsonRpcRequest) => {
  // 第二个监听器：更新UI状态
  const action = jsonRpcRequest.params.action;
  updateUIState(action);
});

sdk.onBusinessButtonClick((jsonRpcRequest) => {
  // 第三个监听器：发送到后端
  sendToBackend({ event: 'business_selection', jsonRpcRequest });
});
```

### 3. 与业务逻辑集成

```javascript
class BusinessHandler {
  constructor(sdk) {
    this.sdk = sdk;
    this.setupEventListeners();
  }

  setupEventListeners() {
    this.sdk.onBusinessButtonClick((jsonRpcRequest) => {
      const action = jsonRpcRequest.params.action;
      this.handleBusinessSelection(action, jsonRpcRequest);
    });
  }

  async handleBusinessSelection(action, jsonRpcRequest) {
    try {
      // 显示加载状态
      this.showLoading(action);

      // 根据业务类型执行相应操作
      const result = await this.processBusinessAction(action);

      // 处理结果
      this.handleResult(action, result, jsonRpcRequest);
    } catch (error) {
      this.handleError(action, error, jsonRpcRequest);
    }
  }

  async processBusinessAction(action) {
    const businessHandlers = {
      'AccountManagement': () => this.handleAccountManagement(),
      'Transfer': () => this.handleTransfer(),
      'AccountInquiry': () => this.handleAccountInquiry(),
      // ... 其他业务处理器
    };

    const handler = businessHandlers[action];
    if (handler) {
      return await handler();
    } else {
      throw new Error(`不支持的业务类型: ${action}`);
    }
  }

  async handleAccountManagement() {
    // 账户管理业务逻辑
    return await api.getAccountInfo();
  }

  async handleTransfer() {
    // 转账业务逻辑
    return await api.getTransferOptions();
  }

  // ... 其他业务处理方法
}

// 使用示例
const businessHandler = new BusinessHandler(sdk);
```

## 事件格式

当用户点击业务按钮时，SDK会发送以下格式的JSON-RPC事件：

```json
{
  "jsonrpc": "2.0",
  "id": "req_xxx",
  "method": "clientUI",   
  "params": {
    "action": "AccountManagement"
  }    
}
```

### 字段说明

- `jsonrpc`: 固定值 "2.0"，表示JSON-RPC 2.0协议
- `id`: 唯一的请求ID，由SDK自动生成
- `method`: 固定值 "clientUI"，表示客户端UI事件
- `params.action`: 业务按钮的ID，对应上表中的按钮ID

## 注意事项

1. **SDK状态检查**：确保SDK已初始化且连接正常后再注册监听器
2. **事件过滤**：监听器只会接收到`method`为`clientUI`的事件
3. **错误处理**：建议在监听器中添加适当的错误处理逻辑
4. **性能考虑**：避免在监听器中执行耗时操作，建议使用异步处理

## 示例项目

查看 `examples/business-button-events-demo.html` 文件，了解完整的使用示例。

## 测试

运行以下命令来测试业务按钮事件功能：

```bash
pnpm test tests/business-button-events.test.ts
```

## 故障排除

### 1. 监听器没有被调用

- 检查SDK是否已正确初始化
- 确认打招呼页面已显示
- 验证点击的是"全部业务"区域的按钮

### 2. 接收到意外的action值

- 检查AllServices组件中的按钮ID配置
- 确认使用的是最新版本的SDK

### 3. 事件重复触发

- 检查是否注册了多个相同的监听器
- 确认组件没有重复渲染

## API参考

### `sdk.onBusinessButtonClick(callback)`

注册业务按钮点击事件监听器。

**参数：**
- `callback: (jsonRpcRequest: any) => void` - 事件回调函数，接收完整的JSON-RPC请求对象

**返回值：**
- `void`

**示例：**
```javascript
sdk.onBusinessButtonClick((jsonRpcRequest) => {
  console.log('业务按钮被点击:', jsonRpcRequest);
  console.log('按钮ID:', jsonRpcRequest.params.action);
  console.log('请求ID:', jsonRpcRequest.id);
});
```

## 更新日志

### v1.0.0
- ✅ 初始实现业务按钮点击事件监听功能
- ✅ 添加9个业务按钮的ID映射
- ✅ 实现JSON-RPC格式的事件发送机制
- ✅ 提供外部监听API
- ✅ 完整的测试覆盖
