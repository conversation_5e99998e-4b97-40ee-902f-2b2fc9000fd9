/**
 * JSON-RPC 2.0 相关类型定义
 * 严格遵循JSON-RPC 2.0规范和docs/json-rpc.md文档
 */

// ============================================================================
// 基础JSON-RPC 2.0类型（严格按照文档定义）
// ============================================================================

/** JSON-RPC 2.0 基础消息接口 */
export interface JsonRpcMessage {
  /** JSON-RPC版本，必须为"2.0" */
  jsonrpc: '2.0';
  /** 方法名，用/连接 */
  method: string;
  /** 方法参数JSON（可选） */
  params?: unknown;
  /** 请求ID，一个请求唯一对应一个响应 */
  id: string | number;
}

/** JSON-RPC 2.0 请求 */
export interface JsonRpcRequest {
  /** JSON-RPC版本，必须为"2.0" */
  jsonrpc: '2.0';
  /** 方法名，用/连接 */
  method: string;
  /** 方法参数JSON（可选） */
  params?: unknown;
  /** 请求ID，一个请求唯一对应一个响应 */
  id: string | number;
}

/** JSON-RPC 2.0 成功响应 */
export interface JsonRpcResponse {
  /** JSON-RPC版本，必须为"2.0" */
  jsonrpc: '2.0';
  /** 响应格式JSON */
  result: unknown;
  /** 响应ID，一个请求唯一对应一个响应 */
  id: string | number;
}

/** JSON-RPC 2.0 错误对象 */
export interface JsonRpcError {
  /** 错误码 */
  code: number;
  /** 错误消息 */
  message: string;
  /** 错误数据（可选） */
  data?: unknown;
}

/** JSON-RPC 2.0 错误响应 */
export interface JsonRpcErrorResponse {
  /** JSON-RPC版本，必须为"2.0" */
  jsonrpc: '2.0';
  /** 报错响应格式JSON */
  error: JsonRpcError;
  /** 错误响应也要匹配原始请求 */
  id: string | number;
}

/** JSON-RPC 2.0 通知（无id字段） */
export interface JsonRpcNotification {
  /** JSON-RPC版本，必须为"2.0" */
  jsonrpc: '2.0';
  /** 方法名，用/连接 */
  method: string;
  /** 方法参数JSON（可选） */
  params?: unknown;
}

/** 请求上下文接口 */
export interface RequestContext {
  /** 设备ID */
  deviceId: string;
  /** 组织ID（可选） */
  organizationId?: number;
  /** 语言设置（可选） */
  locale?: string;
}

/** 扩展的JSON-RPC请求（包含context） */
export interface JsonRpcRequestWithContext extends JsonRpcRequest {
  /** 请求上下文 */
  context: RequestContext;
}

/** JSON-RPC消息发送选项 */
export interface JsonRpcMessageOptions {
  /** 超时时间（毫秒），默认30秒 */
  timeout?: number;
}

// ============================================================================
// 业务按钮相关类型
// ============================================================================

/** 业务按钮ID类型 */
export type BusinessButtonId =
  | 'AccountManagement'
  | 'AccountInquiry'
  | 'BusinessContract'
  | 'Transfer'
  | 'SavingsBalance'
  | 'InvestmentFinancialManagement'
  | 'ConvenienceServices'
  | 'CreditCardBusiness'
  | 'Cash';

/** 常用功能按钮ID类型 */
export type QuickActionButtonId =
  | 'ElectronicChannels'
  | 'SelfServiceCard'
  | 'NonCounterLimit'
  | 'HappinessDepositSigningContract'
  | 'TransactionHistoryInquiry'
  | 'PersonalInformation'
  | 'YinTongModify'
  | 'RegularOpenAccount'
  | 'BankTransfer'
  | 'ReportLossAndReissue';

/** clientUI方法参数 */
export interface ClientUIParams {
  /** 按钮ID - 可以是业务按钮或常用功能按钮 */
  action: BusinessButtonId | QuickActionButtonId;
  /** 按钮名称（中文） */
  name?: string;
  /** 业务数据（可选） */
  data?: unknown;
}

/** clientUI方法的JSON-RPC请求 */
export interface ClientUIRequest extends JsonRpcRequest {
  method: 'clientUI';
  params: ClientUIParams;
}

/** 业务按钮点击事件回调函数类型 */
export type BusinessButtonClickCallback = (request: ClientUIRequest) => void;

// ============================================================================
// 文档中定义的具体方法参数类型
// ============================================================================

/** speak方法参数 */
export interface SpeakParams {
  /** 说话内容，编码UTF-8，必填 */
  text: string;
  /** 非负整数，延时delay毫秒数后说话，可选，缺省值为0 */
  delay?: number;
  /** 是否显示到气泡上，可选，缺省值为true */
  display?: boolean;
}

/** updateBackgroundInfo方法参数 - 参数不固定，使用any类型 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type UpdateBackgroundInfoParams = any;

/** addMessages方法参数 */
export interface AddMessagesParams {
  /** 会话id，如果传的有值，则找到对应会话更新数据，如果没传值，会在响应消息里带上sessionId */
  sessionId?: string;
  /** 消息列表，必须符合OpenAI的消息格式 */
  messages: Array<{
    role: 'user' | 'assistant';
    content: string;
  }>;
}

/** pushBizData方法参数 */
export interface PushBizDataParams {
  /** 业务数据的key值 */
  key: string;
  /** 业务数据 */
  data: unknown;
}



/** speakMode方法参数 */
export interface SpeakModeParams {
  /** 收音模式：0唇动收音，1点击收音 */
  speakMode: 0 | 1;
}

// ============================================================================
// 通知相关类型（按照文档定义）
// ============================================================================

/** 模型状态通知参数 */
export interface ModelStatusNotificationParams {
  /** 模型是否已加载 */
  loaded: boolean;
}

/** 人脸状态通知参数 */
export interface FaceStatusNotificationParams {
  /** 是否有人脸 */
  hasFace: boolean;
}

/** ASR离线识别结果通知参数 */
export interface ASROfflineResultParams {
  /** 会话ID */
  sid: string;
  /** 识别文本 */
  text: string;
}

/** ASR会话完成通知参数 */
export interface ASRSessionCompleteParams {
  // 目前文档中没有具体参数定义，使用Record类型避免空接口
  [key: string]: unknown;
}

/** 用户输入通知参数 */
export interface UserInputNotificationParams {
  /** 用户语音输入的内容 */
  userInput: string;
  /** 请求id */
  requestId: string;
  /** 会话Id */
  sessionId: string;
}

/** 新用户通知参数 */
export interface NewUserNotificationParams {
  // 目前文档中没有具体参数定义，使用Record类型避免空接口
  [key: string]: unknown;
}

/** 聊天流式响应通知参数 */
export interface ChatStreamResponseParams {
  /** 流式返回文本 */
  message: string;
  /** 请求id */
  requestId: string;
  /** 会话Id */
  sessionId: string;
}

/** 状态通知参数 */
export interface StatusNotificationParams {
  /** 原始请求ID */
  requestId: string;
  /** 状态消息 */
  message: string;
}

/** AI响应通知参数 */
export interface AIResponseNotificationParams {
  /** 原始请求ID */
  requestId: string;
  /** 响应消息 */
  message: string;
  /** 操作类型 */
  action?: string;
  /** 更新类型（用于update action） */
  type?: string;
  /** 操作数据 */
  data?: unknown;
}

// ============================================================================
// 响应结果类型
// ============================================================================

/** 会话相关响应结果 */
export interface SessionResult {
  /** 会话ID */
  sessionId: string;
}

/** 推送业务数据响应结果 */
export interface PushBizDataResult {
  /** 成功标志 */
  success: boolean;
}

/** AI响应结果基础类型 */
export interface AIResponseResult {
  /** 响应消息 */
  message: string;
  /** 操作类型 */
  action?: string;
  /** 更新类型（用于update action） */
  type?: string;
  /** 操作数据 */
  data?: unknown;
  /** 链式响应的下一个请求ID */
  nextRequestId?: string;
}

// ============================================================================
// 回调函数类型
// ============================================================================

/** 通知回调函数类型 */
export type NotificationCallback = (params: unknown) => void;

// ============================================================================
// 错误码常量（按照文档定义）
// ============================================================================

/** JSON-RPC 2.0 标准错误码 */
export const JsonRpcErrorCodes = {
  /** 解析错误 - JSON格式错误、语法错误 */
  PARSE_ERROR: -32700,
  /** 无效请求 - 缺少必需字段、字段类型错误 */
  INVALID_REQUEST: -32600,
  /** 方法未找到 - 调用不存在的方法 */
  METHOD_NOT_FOUND: -32601,
  /** 无效参数 - 参数类型错误、缺少必需参数 */
  INVALID_PARAMS: -32602,
  /** 内部错误 - 服务器内部处理错误 */
  INTERNAL_ERROR: -32603,
} as const;

// ============================================================================
// 类型守卫函数
// ============================================================================

/** 检查是否为JSON-RPC请求 */
export function isJsonRpcRequest(obj: unknown): obj is JsonRpcRequest {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'jsonrpc' in obj &&
    (obj as Record<string, unknown>).jsonrpc === '2.0' &&
    'method' in obj &&
    typeof (obj as Record<string, unknown>).method === 'string' &&
    'id' in obj &&
    (typeof (obj as Record<string, unknown>).id === 'string' ||
      typeof (obj as Record<string, unknown>).id === 'number')
  );
}

/** 检查是否为JSON-RPC通知 */
export function isJsonRpcNotification(obj: unknown): obj is JsonRpcNotification {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'jsonrpc' in obj &&
    (obj as Record<string, unknown>).jsonrpc === '2.0' &&
    'method' in obj &&
    typeof (obj as Record<string, unknown>).method === 'string' &&
    !('id' in obj)
  );
}

/** 检查是否为clientUI请求 */
export function isClientUIRequest(obj: unknown): obj is ClientUIRequest {
  return (
    isJsonRpcRequest(obj) &&
    obj.method === 'clientUI' &&
    typeof obj.params === 'object' &&
    obj.params !== null &&
    'action' in obj.params &&
    typeof (obj.params as Record<string, unknown>).action === 'string'
  );
}

/** 检查是否为speak参数 */
export function isSpeakParams(obj: unknown): obj is SpeakParams {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'text' in obj &&
    typeof (obj as Record<string, unknown>).text === 'string' &&
    ((obj as Record<string, unknown>).text as string).length > 0
  );
}

/** 检查是否为updateBackgroundInfo参数 */
export function isUpdateBackgroundInfoParams(obj: unknown): obj is UpdateBackgroundInfoParams {
  // updateBackgroundInfo参数是any类型，只要是对象就认为有效
  return typeof obj === 'object' && obj !== null;
}

/** 检查是否为addMessages参数 */
export function isAddMessagesParams(obj: unknown): obj is AddMessagesParams {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'messages' in obj &&
    Array.isArray((obj as Record<string, unknown>).messages)
  );
}

/** 检查是否为pushBizData参数 */
export function isPushBizDataParams(obj: unknown): obj is PushBizDataParams {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'key' in obj &&
    typeof (obj as Record<string, unknown>).key === 'string' &&
    'data' in obj
  );
}

/** 检查是否为speakMode参数 */
export function isSpeakModeParams(obj: unknown): obj is SpeakModeParams {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'speakMode' in obj &&
    ((obj as Record<string, unknown>).speakMode === 0 ||
      (obj as Record<string, unknown>).speakMode === 1)
  );
}
