/**
 * 业务按钮点击事件功能测试
 * 验证业务按钮点击事件的发送和监听功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

import { EventBus } from '../src/core/EventBus';
import { WebSDK } from '../src/core/WebSDK';

// Mock WebSDK的依赖
vi.mock('../src/services/TransportManager');
vi.mock('../src/services/HKSTTClient');
vi.mock('../src/services/AIClient');
vi.mock('../src/services/ServiceCoordinator');
vi.mock('../src/routing/MessageRouter');
vi.mock('../src/jsonrpc/JsonRpcRequestManager');
vi.mock('../src/jsonrpc/JsonRpcNotificationManager');
vi.mock('../src/jsonrpc/JsonRpcBusinessHandler');
vi.mock('../src/ui/GreetingPageManager');

describe('业务按钮点击事件功能测试', () => {
  let eventBus: EventBus;
  let sdk: WebSDK;

  beforeEach(() => {
    eventBus = new EventBus();

    // 创建一个简化的SDK实例用于测试
    sdk = {
      getEventBus: () => eventBus,
      getStatus: () => ({ isReady: true, isInitialized: true, isConnected: true }),
      onBusinessButtonClick: (callback: (jsonRpcRequest: any) => void) => {
        // 监听JSON-RPC通知事件，过滤出clientUI方法的事件
        eventBus.on('jsonrpc:notification', (data: any) => {
          if (data && data.method === 'clientUI' && data.params && data.params.action) {
            callback(data);
          }
        });
      },
    } as any;
  });

  afterEach(() => {
    eventBus.clear();
  });

  describe('业务按钮ID配置测试', () => {
    it('应该包含所有9个业务按钮的正确ID', () => {
      const expectedBusinessIds = [
        'AccountManagement',      // 账户管理
        'AccountInquiry',         // 查询业务
        'BusinessContract',       // 业务签约
        'Transfer',               // 转账汇款
        'SavingsBalance',         // 储蓄存款
        'InvestmentFinancialManagement', // 投资理财
        'ConvenienceServices',    // 政务民生
        'CreditCardBusiness',     // 信用卡业务
        'Cash',                   // 现金业务
      ];

      // 验证所有预期的业务ID都存在
      expect(expectedBusinessIds).toHaveLength(9);
      expect(expectedBusinessIds).toContain('AccountManagement');
      expect(expectedBusinessIds).toContain('AccountInquiry');
      expect(expectedBusinessIds).toContain('BusinessContract');
      expect(expectedBusinessIds).toContain('Transfer');
      expect(expectedBusinessIds).toContain('SavingsBalance');
      expect(expectedBusinessIds).toContain('InvestmentFinancialManagement');
      expect(expectedBusinessIds).toContain('ConvenienceServices');
      expect(expectedBusinessIds).toContain('CreditCardBusiness');
      expect(expectedBusinessIds).toContain('Cash');
    });
  });

  describe('JSON-RPC事件发送测试', () => {
    it('应该能够发送正确格式的JSON-RPC clientUI事件', () => {
      const receivedEvents: any[] = [];

      // 监听JSON-RPC通知事件
      eventBus.on('jsonrpc:notification', (data) => {
        receivedEvents.push(data);
      });

      // 模拟业务按钮点击，发送JSON-RPC事件
      const testAction = 'AccountManagement';
      const testRequestId = 'test-req-001';

      eventBus.emit('jsonrpc:notification', {
        jsonrpc: '2.0',
        id: testRequestId,
        method: 'clientUI',
        params: {
          action: testAction,
        },
      });

      // 验证事件被正确发送
      expect(receivedEvents).toHaveLength(1);

      const event = receivedEvents[0];
      expect(event.jsonrpc).toBe('2.0');
      expect(event.id).toBe(testRequestId);
      expect(event.method).toBe('clientUI');
      expect(event.params.action).toBe(testAction);
    });

    it('应该为每个业务按钮发送对应的action', () => {
      const receivedEvents: any[] = [];

      eventBus.on('jsonrpc:notification', (data) => {
        receivedEvents.push(data);
      });

      const businessActions = [
        'AccountManagement',
        'AccountInquiry',
        'BusinessContract',
        'Transfer',
        'SavingsBalance',
        'InvestmentFinancialManagement',
        'ConvenienceServices',
        'CreditCardBusiness',
        'Cash',
      ];

      // 模拟点击每个业务按钮
      businessActions.forEach((action, index) => {
        eventBus.emit('jsonrpc:notification', {
          jsonrpc: '2.0',
          id: `test-req-${index}`,
          method: 'clientUI',
          params: { action },
        });
      });

      // 验证所有事件都被发送
      expect(receivedEvents).toHaveLength(9);

      businessActions.forEach((expectedAction, index) => {
        expect(receivedEvents[index].params.action).toBe(expectedAction);
      });
    });
  });

  describe('事件监听功能测试', () => {
    it('应该能够监听业务按钮点击事件', () => {
      const receivedRequests: any[] = [];

      // 注册业务按钮点击事件监听器
      sdk.onBusinessButtonClick((jsonRpcRequest: any) => {
        receivedRequests.push(jsonRpcRequest);
      });

      // 模拟发送业务按钮点击事件
      const testRequest = {
        jsonrpc: '2.0',
        id: 'test-req-001',
        method: 'clientUI',
        params: {
          action: 'AccountManagement',
        },
      };

      eventBus.emit('jsonrpc:notification', testRequest);

      // 验证监听器被正确调用
      expect(receivedRequests).toHaveLength(1);
      expect(receivedRequests[0]).toEqual(testRequest);
      expect(receivedRequests[0].params.action).toBe('AccountManagement');
    });

    it('应该只监听clientUI方法的事件', () => {
      const receivedRequests: any[] = [];

      sdk.onBusinessButtonClick((jsonRpcRequest: any) => {
        receivedRequests.push(jsonRpcRequest);
      });

      // 发送非clientUI方法的事件
      eventBus.emit('jsonrpc:notification', {
        jsonrpc: '2.0',
        id: 'test-req-001',
        method: 'otherMethod',
        params: {
          action: 'ShouldNotReceive',
        },
      });

      // 发送clientUI方法的事件
      const validRequest = {
        jsonrpc: '2.0',
        id: 'test-req-002',
        method: 'clientUI',
        params: {
          action: 'AccountManagement',
        },
      };

      eventBus.emit('jsonrpc:notification', validRequest);

      // 验证只接收到clientUI方法的事件
      expect(receivedRequests).toHaveLength(1);
      expect(receivedRequests[0]).toEqual(validRequest);
      expect(receivedRequests[0].params.action).toBe('AccountManagement');
    });

    it('应该支持多个监听器', () => {
      const receivedRequests1: any[] = [];
      const receivedRequests2: any[] = [];

      // 注册多个监听器
      sdk.onBusinessButtonClick((jsonRpcRequest: any) => {
        receivedRequests1.push(jsonRpcRequest);
      });

      sdk.onBusinessButtonClick((jsonRpcRequest: any) => {
        receivedRequests2.push(jsonRpcRequest);
      });

      // 发送事件
      const testRequest = {
        jsonrpc: '2.0',
        id: 'test-req-001',
        method: 'clientUI',
        params: {
          action: 'Transfer',
        },
      };

      eventBus.emit('jsonrpc:notification', testRequest);

      // 验证所有监听器都被调用
      expect(receivedRequests1).toHaveLength(1);
      expect(receivedRequests1[0]).toEqual(testRequest);
      expect(receivedRequests1[0].params.action).toBe('Transfer');
      expect(receivedRequests2).toHaveLength(1);
      expect(receivedRequests2[0]).toEqual(testRequest);
      expect(receivedRequests2[0].params.action).toBe('Transfer');
    });
  });

  describe('事件格式验证测试', () => {
    it('应该验证JSON-RPC 2.0格式', () => {
      const receivedEvents: any[] = [];

      eventBus.on('jsonrpc:notification', (data) => {
        receivedEvents.push(data);
      });

      const validEvent = {
        jsonrpc: '2.0',
        id: 'test-req-001',
        method: 'clientUI',
        params: {
          action: 'AccountManagement',
        },
      };

      eventBus.emit('jsonrpc:notification', validEvent);

      expect(receivedEvents).toHaveLength(1);
      const event = receivedEvents[0];

      // 验证JSON-RPC 2.0格式
      expect(event.jsonrpc).toBe('2.0');
      expect(event.method).toBe('clientUI');
      expect(event.params).toBeDefined();
      expect(event.params.action).toBeDefined();
      expect(typeof event.params.action).toBe('string');
    });

    it('应该只包含action参数，不包含data字段', () => {
      const receivedRequests: any[] = [];
      let receivedParams: any = null;

      sdk.onBusinessButtonClick((jsonRpcRequest: any) => {
        receivedRequests.push(jsonRpcRequest);
      });

      eventBus.on('jsonrpc:notification', (data) => {
        if (data && data.method === 'clientUI') {
          receivedParams = data.params;
        }
      });

      eventBus.emit('jsonrpc:notification', {
        jsonrpc: '2.0',
        id: 'test-req-001',
        method: 'clientUI',
        params: {
          action: 'AccountManagement',
        },
      });

      // 验证只有action参数
      expect(receivedParams).toBeDefined();
      expect(receivedParams.action).toBe('AccountManagement');
      expect(receivedParams.data).toBeUndefined();
      expect(Object.keys(receivedParams)).toEqual(['action']);

      // 验证监听器接收到完整的JSON-RPC请求
      expect(receivedRequests).toHaveLength(1);
      expect(receivedRequests[0].params.action).toBe('AccountManagement');
    });
  });
});
