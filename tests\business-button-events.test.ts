/**
 * 业务按钮点击事件功能测试
 * 测试业务按钮点击事件的发送、监听和处理功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { testUtils } from './setup';

describe('业务按钮点击事件功能测试', () => {
  let mockEventBus: any;

  beforeEach(() => {
    mockEventBus = testUtils.createMockEventBus();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('常用功能按钮ID配置', () => {
    it('应该包含所有10个常用功能按钮的正确ID', () => {
      // 定义期望的常用功能按钮ID列表
      const expectedQuickActionIds = [
        'ElectronicChannels',
        'SelfServiceCard',
        'NonCounterLimit',
        'HappinessDepositSigningContract',
        'TransactionHistoryInquiry',
        'PersonalInformation',
        'YinTongModify',
        'RegularOpenAccount',
        'BankTransfer',
        'ReportLossAndReissue',
      ];

      // 验证每个ID都是字符串且符合命名规范
      expectedQuickActionIds.forEach(id => {
        expect(typeof id).toBe('string');
        expect(id).toMatch(/^[A-Z][a-zA-Z]*$/); // 首字母大写的驼峰命名
        expect(id.length).toBeGreaterThan(3); // 至少4个字符
      });

      // 验证总数
      expect(expectedQuickActionIds).toHaveLength(10);

      // 验证没有重复
      const uniqueIds = new Set(expectedQuickActionIds);
      expect(uniqueIds.size).toBe(expectedQuickActionIds.length);
    });
  });

  describe('业务分类按钮ID配置', () => {
    it('应该包含所有9个业务分类按钮的正确ID', () => {
      // 定义期望的业务分类按钮ID列表
      const expectedBusinessIds = [
        'AccountManagement',
        'AccountInquiry',
        'BusinessContract',
        'Transfer',
        'SavingsBalance',
        'InvestmentFinancialManagement',
        'ConvenienceServices',
        'CreditCardBusiness',
        'Cash',
      ];

      // 验证每个ID都是字符串且符合命名规范
      expectedBusinessIds.forEach(id => {
        expect(typeof id).toBe('string');
        expect(id).toMatch(/^[A-Z][a-zA-Z]*$/); // 首字母大写的驼峰命名
        expect(id.length).toBeGreaterThan(3); // 至少4个字符
      });

      // 验证总数
      expect(expectedBusinessIds).toHaveLength(9);

      // 验证没有重复
      const uniqueIds = new Set(expectedBusinessIds);
      expect(uniqueIds.size).toBe(expectedBusinessIds.length);
    });
  });

  describe('JSON-RPC事件发送测试', () => {
    it('应该能够发送正确格式的JSON-RPC clientUI事件', () => {
      const receivedEvents: any[] = [];

      // 监听JSON-RPC通知事件
      mockEventBus.on('jsonrpc:notification', (data: any) => {
        receivedEvents.push(data);
      });

      // 模拟常用功能按钮点击，发送JSON-RPC事件
      const testAction = 'ElectronicChannels';
      const testName = '电子渠道签约';
      const testRequestId = 'test-req-001';

      mockEventBus.emit('jsonrpc:notification', {
        jsonrpc: '2.0',
        id: testRequestId,
        method: 'clientUI',
        params: {
          action: testAction,
          name: testName,
        },
      });

      // 验证事件被正确发送
      expect(receivedEvents).toHaveLength(1);

      const event = receivedEvents[0];
      expect(event.jsonrpc).toBe('2.0');
      expect(event.id).toBe(testRequestId);
      expect(event.method).toBe('clientUI');
      expect(event.params.action).toBe(testAction);
      expect(event.params.name).toBe(testName);
    });

    it('应该为每个按钮发送对应的action和name', () => {
      const receivedEvents: any[] = [];

      mockEventBus.on('jsonrpc:notification', (data: any) => {
        receivedEvents.push(data);
      });

      const buttonData = [
        { action: 'ElectronicChannels', name: '电子渠道签约' },
        { action: 'SelfServiceCard', name: '开卡' },
        { action: 'BankTransfer', name: '转账' },
        { action: 'AccountManagement', name: '账户管理' },
        { action: 'Transfer', name: '转账汇款' },
      ];

      // 为每个按钮发送事件
      buttonData.forEach((button, index) => {
        mockEventBus.emit('jsonrpc:notification', {
          jsonrpc: '2.0',
          id: `req-${index}`,
          method: 'clientUI',
          params: {
            action: button.action,
            name: button.name,
          },
        });
      });

      // 验证所有事件都被发送
      expect(receivedEvents).toHaveLength(buttonData.length);

      // 验证每个事件的action和name正确
      receivedEvents.forEach((event, index) => {
        expect(event.params.action).toBe(buttonData[index].action);
        expect(event.params.name).toBe(buttonData[index].name);
      });
    });
  });

  describe('事件监听功能测试', () => {
    it('应该能够监听按钮点击事件', () => {
      const receivedEvents: any[] = [];
      let listenerCallCount = 0;

      // 注册监听器
      const listener = (data: any) => {
        listenerCallCount++;
        receivedEvents.push(data);
      };

      mockEventBus.on('jsonrpc:notification', listener);

      // 发送测试事件
      const testEvent = {
        jsonrpc: '2.0',
        id: 'test-001',
        method: 'clientUI',
        params: {
          action: 'BankTransfer',
          name: '转账',
        },
      };

      mockEventBus.emit('jsonrpc:notification', testEvent);

      // 验证监听器被调用
      expect(listenerCallCount).toBe(1);
      expect(receivedEvents).toHaveLength(1);
      expect(receivedEvents[0]).toEqual(testEvent);
    });

    it('应该只监听clientUI方法的事件', () => {
      const clientUIEvents: any[] = [];
      const otherEvents: any[] = [];

      // 注册监听器
      mockEventBus.on('jsonrpc:notification', (data: any) => {
        if (data.method === 'clientUI') {
          clientUIEvents.push(data);
        } else {
          otherEvents.push(data);
        }
      });

      // 发送不同类型的事件
      const events = [
        { jsonrpc: '2.0', id: '1', method: 'clientUI', params: { action: 'BankTransfer', name: '转账' } },
        { jsonrpc: '2.0', id: '2', method: 'otherMethod', params: { data: 'test' } },
        { jsonrpc: '2.0', id: '3', method: 'clientUI', params: { action: 'AccountManagement', name: '账户管理' } },
        { jsonrpc: '2.0', id: '4', method: 'anotherMethod', params: { value: 123 } },
      ];

      events.forEach(event => {
        mockEventBus.emit('jsonrpc:notification', event);
      });

      // 验证只有clientUI事件被正确分类
      expect(clientUIEvents).toHaveLength(2);
      expect(otherEvents).toHaveLength(2);
      expect(clientUIEvents[0].params.action).toBe('BankTransfer');
      expect(clientUIEvents[1].params.action).toBe('AccountManagement');
    });

    it('应该支持多个监听器', () => {
      let listener1CallCount = 0;
      let listener2CallCount = 0;
      let listener3CallCount = 0;

      // 注册多个监听器
      mockEventBus.on('jsonrpc:notification', () => { listener1CallCount++; });
      mockEventBus.on('jsonrpc:notification', () => { listener2CallCount++; });
      mockEventBus.on('jsonrpc:notification', () => { listener3CallCount++; });

      // 发送事件
      mockEventBus.emit('jsonrpc:notification', {
        jsonrpc: '2.0',
        id: 'test',
        method: 'clientUI',
        params: { action: 'PersonalInformation', name: '个人信息修改' },
      });

      // 验证所有监听器都被调用
      expect(listener1CallCount).toBe(1);
      expect(listener2CallCount).toBe(1);
      expect(listener3CallCount).toBe(1);
    });
  });

  describe('事件格式验证测试', () => {
    it('应该验证JSON-RPC 2.0格式', () => {
      const receivedEvents: any[] = [];

      mockEventBus.on('jsonrpc:notification', (data: any) => {
        receivedEvents.push(data);
      });

      const validEvent = {
        jsonrpc: '2.0',
        id: 'test-id',
        method: 'clientUI',
        params: {
          action: 'RegularOpenAccount',
          name: '定期开户',
        },
      };

      mockEventBus.emit('jsonrpc:notification', validEvent);

      expect(receivedEvents).toHaveLength(1);
      const event = receivedEvents[0];

      // 验证JSON-RPC 2.0格式
      expect(event.jsonrpc).toBe('2.0');
      expect(event.id).toBeDefined();
      expect(event.method).toBeDefined();
      expect(event.params).toBeDefined();
      expect(typeof event.params).toBe('object');
    });

    it('应该包含action和name参数', () => {
      const receivedEvents: any[] = [];

      mockEventBus.on('jsonrpc:notification', (data: any) => {
        receivedEvents.push(data);
      });

      const event = {
        jsonrpc: '2.0',
        id: 'test-id',
        method: 'clientUI',
        params: {
          action: 'YinTongModify',
          name: '银信通修改',
        },
      };

      mockEventBus.emit('jsonrpc:notification', event);

      expect(receivedEvents).toHaveLength(1);
      const receivedEvent = receivedEvents[0];

      // 验证params结构
      expect(receivedEvent.params.action).toBeDefined();
      expect(receivedEvent.params.name).toBeDefined();
      expect(receivedEvent.params.action).toBe('YinTongModify');
      expect(receivedEvent.params.name).toBe('银信通修改');
    });
  });
});
