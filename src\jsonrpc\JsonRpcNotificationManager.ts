/**
 * JSON-RPC 通知管理器
 * 负责管理通知监听器和处理特殊通知
 */

import { EventBus } from '../core/EventBus';
import {
  StatusNotificationParams,
  ChatStreamResponseParams,
  UserInputNotificationParams,
  NewUserNotificationParams,
  FaceStatusNotificationParams,
  ModelStatusNotificationParams,
  ASROfflineResultParams,
  ASRSessionCompleteParams,
} from '../types/jsonrpc';
import { Logger } from '../utils/Logger';

import { JsonRpcValidator } from './JsonRpcValidator';

/**
 * 通知回调函数类型
 */
export type NotificationCallback = (params: unknown) => void;

/**
 * JSON-RPC 通知管理器
 */
export class JsonRpcNotificationManager {
  private eventBus: EventBus;
  private logger: Logger;
  private validator: JsonRpcValidator;
  private notificationListeners = new Map<string, NotificationCallback[]>();

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({ prefix: 'JsonRpcNotificationManager' });
    this.validator = new JsonRpcValidator();

    this.setupEventListeners();
  }

  /**
   * 监听JSON-RPC通知
   * @param method 通知方法名
   * @param callback 回调函数
   * @returns 取消监听的函数
   */
  public onNotification(method: string, callback: NotificationCallback): () => void {
    if (!this.notificationListeners.has(method)) {
      this.notificationListeners.set(method, []);
    }

    const listeners = this.notificationListeners.get(method);
    if (listeners) {
      listeners.push(callback);

      this.logger.info('📝 注册通知监听器', {
        method,
        listenerCount: listeners.length,
      });
    }

    // 返回取消监听的函数
    return () => {
      const currentListeners = this.notificationListeners.get(method);
      if (currentListeners) {
        const index = currentListeners.indexOf(callback);
        if (index > -1) {
          currentListeners.splice(index, 1);
          this.logger.info('🗑️ 移除通知监听器', {
            method,
            remainingCount: currentListeners.length,
          });

          // 如果没有监听器了，删除整个条目
          if (currentListeners.length === 0) {
            this.notificationListeners.delete(method);
          }
        }
      }
    };
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.logger.info('🗑️ 销毁通知管理器');

    // 清空所有监听器
    this.notificationListeners.clear();

    // 清理事件监听器
    this.eventBus.off('jsonrpc:notification', this.handleNotification);

    this.logger.info('✅ 通知管理器已销毁');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听JSON-RPC通知
    this.eventBus.on('jsonrpc:notification', this.handleNotification);
    // 注意：client:send-notification 是用来发送通知给客户端的，不应该在这里监听
  }

  /**
   * 处理收到的JSON-RPC通知
   */
  private handleNotification = (notification: unknown): void => {
    // 验证通知格式
    if (!this.validator.validateNotification(notification)) {
      this.logger.warn('收到无效的JSON-RPC通知', { notification });
      return;
    }

    const notif = notification as { method: string; params?: unknown };
    const method = notif.method;
    const params = notif.params;

    this.logger.info('📥 收到JSON-RPC通知', {
      method,
      hasParams: params !== undefined,
    });

    // 处理特殊的通知类型
    this.handleSpecialNotifications(method, params);

    // 查找并调用监听器
    const listeners = this.notificationListeners.get(method);
    if (listeners && listeners.length > 0) {
      listeners.forEach(callback => {
        try {
          callback(params);
        } catch (error) {
          this.logger.error('通知监听器执行失败', { method, error });
        }
      });
    } else {
      this.logger.debug('没有找到通知监听器', { method });
    }
  };

  /**
   * 处理特殊的通知类型（根据json-rpc.md文档）
   */
  private handleSpecialNotifications(method: string, params: unknown): void {
    switch (method) {
      case 'notifications/status':
        if (typeof params === 'object' && params !== null) {
          this.handleStatusNotification(params as StatusNotificationParams);
        }
        break;

      case 'notifications/chatStreamResponse':
        if (typeof params === 'object' && params !== null) {
          this.handleChatStreamResponse(params as ChatStreamResponseParams);
        }
        break;

      case 'notifications/userInput':
        if (typeof params === 'object' && params !== null) {
          this.handleUserInputNotification(params as UserInputNotificationParams);
        }
        break;

      case 'notifications/aiResponse':
        // AI响应通知通过标准的通知监听器处理，不需要特殊处理
        if (typeof params === 'object' && params !== null) {
          const aiParams = params as Record<string, unknown>;
          this.logger.info('🤖 AI响应通知', {
            requestId: aiParams.requestId,
            message:
              typeof aiParams.message === 'string'
                ? aiParams.message.substring(0, 50) + '...'
                : undefined,
            action: aiParams.action,
            hasData: !!aiParams.data,
          });
        }
        break;

      case 'notifications/newUser':
        if (typeof params === 'object' && params !== null) {
          this.handleNewUserNotification(params as NewUserNotificationParams);
        }
        break;

      case 'notifications/faceStatus':
        if (typeof params === 'object' && params !== null) {
          this.handleFaceStatusNotification(params as FaceStatusNotificationParams);
        }
        break;

      case 'notifications/modelStatus':
        if (typeof params === 'object' && params !== null) {
          this.handleModelStatusNotification(params as ModelStatusNotificationParams);
        }
        break;

      case 'notifications/asrOfflineResult':
        if (typeof params === 'object' && params !== null) {
          this.handleASROfflineResultNotification(params as ASROfflineResultParams);
        }
        break;

      case 'notifications/asrSessionComplete':
        if (typeof params === 'object' && params !== null) {
          this.handleASRSessionCompleteNotification(params as ASRSessionCompleteParams);
        }
        break;
    }
  }

  private handleStatusNotification(params: StatusNotificationParams): void {
    const { requestId, message } = params;

    this.logger.debug('� 状态通知', { requestId, message });

    // 状态通知不应该输入到对话框中，只是记录状态
    this.eventBus.emit('ai:status', {
      requestId,
      message,
      timestamp: Date.now(),
    });
  }

  private handleChatStreamResponse(params: ChatStreamResponseParams): void {
    const { message, requestId, sessionId } = params;

    this.logger.debug('💬 聊天流式响应', {
      requestId,
      sessionId,
      messageLength: message?.length,
    });

    this.eventBus.emit('tts:stream-text', {
      text: message,
      requestId,
      sessionId,
    });
  }

  private handleUserInputNotification(params: UserInputNotificationParams): void {
    const { userInput, requestId, sessionId } = params;

    this.logger.info('🎤 用户语音输入', {
      requestId,
      sessionId,
      inputLength: userInput?.length,
    });

    this.eventBus.emit('user:input', {
      userInput,
      requestId,
      sessionId,
      timestamp: Date.now(),
    });
  }

  private handleNewUserNotification(params: NewUserNotificationParams): void {
    this.logger.info('👤 新用户检测', params);

    this.eventBus.emit('user:new', {
      ...params,
      timestamp: Date.now(),
    });
  }

  private handleFaceStatusNotification(params: FaceStatusNotificationParams): void {
    const { hasFace } = params;

    this.logger.info('👁️ 人脸状态变化', { hasFace });

    this.eventBus.emit('face:status', {
      hasFace,
      timestamp: Date.now(),
    });
  }

  private handleModelStatusNotification(params: ModelStatusNotificationParams): void {
    const { loaded } = params;

    this.logger.info('🤖 模型状态', { loaded });

    this.eventBus.emit('model:status', {
      loaded,
      timestamp: Date.now(),
    });
  }

  private handleASROfflineResultNotification(params: ASROfflineResultParams): void {
    const { sid, text } = params;

    this.logger.info('🎯 ASR识别结果', {
      sid,
      textLength: text?.length,
    });

    this.eventBus.emit('asr:result', {
      sid,
      text,
      timestamp: Date.now(),
    });
  }

  private handleASRSessionCompleteNotification(params: ASRSessionCompleteParams): void {
    this.logger.info('✅ ASR会话完成', params);

    this.eventBus.emit('asr:session:complete', {
      ...params,
      timestamp: Date.now(),
    });
  }
}
