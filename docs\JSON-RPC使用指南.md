# JSON-RPC使用指南

## 概述

WebSDK提供统一的JSON-RPC API：`sdk.sendJsonRpcMessage(message, options)`，让用户完全控制JSON-RPC消息格式和请求ID，并返回Promise以获取响应结果。

## 为什么使用JSON-RPC API？

### 设计优势

WebSDK的JSON-RPC API具有以下优势：

1. **完全控制请求ID**：用户可以自定义请求ID，便于调试和监控
2. **标准JSON-RPC 2.0格式**：完全符合JSON-RPC 2.0规范
3. **Promise接口**：支持async/await语法，可以直接获取响应结果
4. **灵活的消息格式**：用户可以发送完整的JSON-RPC消息
5. **更好的可观测性**：便于请求追踪和问题排查
6. **🆕 自动会话管理**：会话ID完全由SDK内部管理，用户无需关心

### 核心功能

`sendJsonRpcMessage`方法提供：

- 完全控制请求ID
- 发送标准的JSON-RPC 2.0消息
- **返回Promise获取响应结果**
- 自动超时处理（默认30秒）
- **🆕 智能会话ID管理**：SDK自动检测活跃组件并使用正确的会话ID
- 更好的调试和监控能力

## 基本使用

### 1. JSON-RPC消息发送

```typescript
import { init } from 'web-service-api-sdk';

const sdk = await init({
  hksttUrl: 'ws://localhost:8001',
  aiServerUrl: 'http://localhost:8080',
  debug: true
});

// 发送完整的JSON-RPC消息并获取响应
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: {
    text: '您好，欢迎使用我们的服务',
    delay: 1000,
    display: true
    // 注意：不再需要传递sessionId，SDK会自动管理
  },
  id: 'my-speak-request-001'  // 用户完全控制ID
});

console.log('响应:', response);
// 输出: {
//   success: true,
//   message: "TTS播放已开始",
//   text: "您好，欢迎使用我们的服务",
//   sessionId: "auto-generated-session-id",  // SDK自动管理的会话ID
//   targetComponent: "greeting-page",        // 当前活跃组件
//   language: "mandarin"                     // 当前语言设置
// }
```

### 2. 请求ID管理和错误处理

```typescript
// 使用自定义ID进行请求跟踪
const requestId = `speak-${Date.now()}`;

try {
  const response = await sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speak',
    params: { text: '测试消息' },  // 不需要传递sessionId
    id: requestId
  });

  console.log(`请求 ${requestId} 成功:`, response);
  // 输出: {
  //   success: true,
  //   message: "TTS播放已开始",
  //   text: "测试消息",
  //   sessionId: "auto-managed-session",  // SDK自动管理
  //   targetComponent: "chat-widget",     // 当前活跃组件
  //   language: "mandarin"                // 当前语言
  // }
} catch (error) {
  console.error(`请求 ${requestId} 失败:`, error);
  // 可能的错误：超时、网络错误、JSON-RPC错误等
}
```

### 3. 批量请求管理

```typescript
// 发送多个相关请求（不再需要手动管理sessionId）
const requests = [
  {
    jsonrpc: '2.0' as const,
    method: 'updateBackgroundInfo',
    params: { page: 'login', status: 'active' },  // 移除sessionId参数
    id: 'bg-update-001'
  },
  {
    jsonrpc: '2.0' as const,
    method: 'addMessages',
    params: {
      // sessionId由SDK自动管理，不需要传递
      messages: [{ role: 'user', content: '我想查询余额' }]
    },
    id: 'add-msg-001'
  }
];

// 并发发送
const responses = await Promise.all(
  requests.map(req => sdk.sendJsonRpcMessage(req))
);

console.log('所有请求完成:', responses);
// 每个响应都包含SDK自动管理的sessionId和调试信息
```

## 高级功能

### 1. 可选验证

```typescript
// 跳过方法名验证（用于扩展方法）
await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'customMethod',
  params: { data: 'test' },
  id: 'custom-001'
}, {
  validateMethod: false,  // 跳过方法名验证
  validateParams: false,  // 跳过参数验证
  timeout: 15000         // 自定义超时
});
```

### 2. 错误处理

```typescript
try {
  await sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speak',
    params: { text: '' },  // 无效参数
    id: 'error-test'
  });
} catch (error) {
  if (error.message.includes('参数格式不正确')) {
    console.log('参数验证失败');
  } else if (error.message.includes('请求ID冲突')) {
    console.log('ID已被使用');
  }
}
```

### 3. 超时配置

```typescript
// 配置请求超时时间（默认30秒）
try {
  const response = await sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speak',
    params: { text: '测试消息' },
    id: 'timeout-test-001'
  }, {
    timeout: 10000  // 10秒超时
  });

  console.log('响应:', response);
} catch (error) {
  if (error.message.includes('请求超时')) {
    console.error('请求超时了');
  } else {
    console.error('其他错误:', error);
  }
}
```

## 支持的方法

当前SDK支持以下JSON-RPC方法。**重要提示**：所有方法的会话ID现在完全由SDK内部管理，用户无需传递sessionId参数。

### 1. speak - 语音播报

```typescript
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: {
    text: '要播报的文本',      // 必需
    delay: 1000,             // 可选，延时毫秒数
    display: true            // 可选，是否显示在气泡中
    // 注意：不再需要传递sessionId，SDK会自动管理
  },
  id: 'speak-001'
});

// 响应示例
console.log(response);
// {
//   success: true,
//   message: "TTS播放已开始",
//   text: "要播报的文本",
//   delay: 1000,
//   display: true,
//   sessionId: "auto-managed-session-id",  // SDK自动管理的会话ID
//   targetComponent: "greeting-page",      // 当前活跃组件
//   language: "mandarin"                   // 当前语言设置
// }
```

### 2. updateBackgroundInfo - 更新背景信息

```typescript
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'updateBackgroundInfo',
  params: {
    // sessionId由SDK自动管理，不需要传递
    page: 'login-page',        // 可选，当前页面
    status: 'active'           // 可选，当前状态
  },
  id: 'bg-update-001'
});

// 响应示例
console.log(response);
// {
//   sessionId: "auto-managed-session-id",  // SDK自动管理的会话ID
//   _debug: {
//     targetComponent: "greeting-page",    // 当前活跃组件
//     sessionId: "auto-managed-session-id",
//     language: "mandarin"                 // 当前语言设置
//   }
// }
```

### 3. addMessages - 添加对话消息

```typescript
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'addMessages',
  params: {
    // sessionId由SDK自动管理，不需要传递
    messages: [                // 必需，消息数组
      { role: 'user', content: '用户消息' },
      { role: 'assistant', content: 'AI回复' }
    ]
  },
  id: 'add-msg-001'
});

// 响应示例
console.log(response);
// {
//   sessionId: "auto-managed-session-id",  // SDK自动管理的会话ID
//   _debug: {
//     targetComponent: "chat-widget",      // 当前活跃组件
//     sessionId: "auto-managed-session-id",
//     language: "mandarin"                 // 当前语言设置
//   }
// }
```

### 4. pushBizData - 推送业务数据

```typescript
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'pushBizData',
  params: {
    // sessionId由SDK自动管理，不需要传递
    key: 'userProfile',        // 必需，数据键名
    data: {                    // 必需，业务数据
      userId: '123',
      name: '张三',
      balance: 1000
    }
  },
  id: 'push-data-001'
});

// 响应示例
console.log(response);
// {
//   success: true,
//   _debug: {
//     targetComponent: "greeting-page",    // 当前活跃组件
//     sessionId: "auto-managed-session-id",
//     language: "mandarin"                 // 当前语言设置
//   }
// }
```

### 5. speakMode - 设置收音模式

```typescript
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speakMode',
  params: {
    speakMode: 0  // 必需，0=唇动收音，1=点击收音
  },
  id: 'speak-mode-001'
});

// 响应示例
console.log(response);
// {
//   success: true,
//   speakMode: 0,
//   message: "收音模式已设置为唇动收音"
// }
```

**注意**：speakMode方法直接与HKSTT服务通信，不涉及会话ID管理。

## 🆕 会话ID自动管理

### 核心特性

从当前版本开始，WebSDK实现了智能的会话ID管理机制：

1. **完全自动化**：用户无需传递sessionId参数，SDK会自动检测当前活跃组件并使用正确的会话ID
2. **智能路由**：消息会自动路由到当前活跃的UI组件（打招呼页面或聊天组件）
3. **语言同步**：TTS播报语言与UI显示语言自动保持一致
4. **调试友好**：返回值包含实际使用的sessionId、目标组件、语言等调试信息

### 活跃组件检测

SDK会自动检测以下信息：

- **当前活跃组件**：greeting-page（打招呼页面）或 custom-component（聊天组件）
- **真实会话ID**：从MessageRouter获取当前组件的会话ID
- **语言设置**：从localStorage获取当前语言设置（mandarin/chuanyu）

### 调试信息

所有方法的返回值都包含调试信息，帮助开发者了解SDK的内部状态：

```typescript
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: { text: '测试消息' },
  id: 'debug-test'
});

console.log('调试信息:', {
  sessionId: response.sessionId,        // SDK自动管理的会话ID
  targetComponent: response.targetComponent,  // 当前活跃组件
  language: response.language           // 当前语言设置
});
```

## 向后兼容性

WebSDK专注于JSON-RPC 2.0标准接口，现在更加简化：

```typescript
// 推荐方式：使用标准JSON-RPC 2.0格式（无需sessionId）
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: { text: '你好' },  // 不再需要传递sessionId
  id: 'my-custom-id'
});

console.log('响应:', response);
// {
//   success: true,
//   message: "TTS播放已开始",
//   text: "你好",
//   sessionId: "auto-managed-session-id",  // SDK自动管理
//   targetComponent: "greeting-page",      // 当前活跃组件
//   language: "mandarin"                   // 当前语言
// }
```

### 迁移指南

如果您的代码中包含sessionId参数，可以安全地移除它们：

```typescript
// 旧版本（仍然兼容，但sessionId会被忽略）
await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'addMessages',
  params: {
    sessionId: 'user-provided-session',  // 这个参数会被忽略
    messages: [{ role: 'user', content: '消息' }]
  },
  id: 'old-style'
});

// 新版本（推荐）
await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'addMessages',
  params: {
    // 移除sessionId参数，SDK会自动管理
    messages: [{ role: 'user', content: '消息' }]
  },
  id: 'new-style'
});
```

## 最佳实践

### 1. 请求ID命名规范

```typescript
// 推荐的ID命名模式（不再需要包含sessionId）
const requestId = `${method}-${timestamp}`;
const requestId2 = `speak-${Date.now()}`;
const requestId3 = `bg-update-login-page-001`;
const requestId4 = `chat-user-query-${Date.now()}`;
```

### 2. 错误重试机制

```typescript
async function sendWithRetry(message, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      // 每次重试使用新的ID
      const retryMessage = {
        ...message,
        id: `${message.id}-retry-${i}`
      };
      
      return await sdk.sendJsonRpcMessage(retryMessage);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

### 3. 请求去重

```typescript
const sentRequests = new Set();

function sendUniqueRequest(message) {
  if (sentRequests.has(message.id)) {
    throw new Error(`请求ID ${message.id} 已发送过`);
  }
  
  sentRequests.add(message.id);
  return sdk.sendJsonRpcMessage(message);
}
```

## 最佳实践

### 请求ID命名规范

```typescript
// 推荐的请求ID格式（简化版）
const requestId = `${method}-${timestamp}-${sequence}`;

// 示例
const result = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: { text: '你好' },  // 不需要sessionId
  id: `speak-${Date.now()}-001`
});

console.log('响应包含自动管理的会话信息:', {
  sessionId: result.sessionId,
  targetComponent: result.targetComponent,
  language: result.language
});
```

### 请求跟踪和监控

```typescript
// 使用有意义的请求ID进行跟踪
const myRequestId = 'user-greeting-request';
const response = await sdk.sendJsonRpcMessage({
  jsonrpc: '2.0',
  method: 'speak',
  params: { text: '你好' },  // 不需要sessionId
  id: myRequestId
});

// 可以跟踪这个特定的请求和SDK的自动管理信息
console.log(`请求 ${myRequestId} 已发送到组件 ${response.targetComponent}`);
console.log(`使用会话ID: ${response.sessionId}`);
console.log(`当前语言: ${response.language}`);
```

### 🆕 会话状态监控

```typescript
// 监控SDK的会话管理状态
const responses = [];

for (const text of ['消息1', '消息2', '消息3']) {
  const response = await sdk.sendJsonRpcMessage({
    jsonrpc: '2.0',
    method: 'speak',
    params: { text },
    id: `batch-speak-${Date.now()}`
  });

  responses.push({
    text,
    sessionId: response.sessionId,
    targetComponent: response.targetComponent,
    language: response.language
  });
}

// 验证所有消息是否使用了一致的会话ID
const uniqueSessionIds = new Set(responses.map(r => r.sessionId));
console.log(`使用了 ${uniqueSessionIds.size} 个不同的会话ID`);
console.log('会话状态:', responses);
```
