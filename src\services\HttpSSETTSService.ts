/**
 * HTTP+SSE TTS服务
 * 基于HTTP POST请求和Server-Sent Events的流式语音合成服务
 * 替代原有的WebSocket实现，支持新的CosyVoice2 API
 */

import { EventBus } from '../core/EventBus';
import { Logger } from '../utils/Logger';

import { StreamingAudioPlayer, PlayerState } from './StreamingAudioPlayer';
import type { ITTSService, TTSStatus, AudioChunk } from './types';

/**
 * SSE事件类型
 */
interface SSEEvent {
  type: string;
  data: string;
}

/**
 * 音频块数据格式
 */
interface AudioChunkData {
  chunk_id: number;
  audio_data: string; // Base64编码的WAV音频数据
  duration?: number; // 音频块时长（秒），可选字段
}

/**
 * 合成结束数据格式
 */
interface SynthesisEndData {
  total_chunks: number;
  total_duration?: number; // 可选字段，服务器可能不返回
  message?: string; // 服务器消息
  mode?: string; // 合成模式
}

/**
 * HTTP+SSE TTS服务配置
 */
export interface HttpSSETTSConfig {
  /** HTTP服务器URL */
  serverUrl: string;
  /** 请求超时时间(ms) */
  requestTimeout?: number;
  /** 最大重试次数 */
  maxRetryAttempts?: number;
  /** 重试延迟(ms) */
  retryDelay?: number;
  /** 固定随机种子 */
  seed?: number;
  /** 是否启用调试日志 */
  debug?: boolean;
}

/**
 * HTTP+SSE TTS服务实现
 */
export class HttpSSETTSService implements ITTSService {
  private eventBus: EventBus;
  private logger: Logger;
  private config: Required<HttpSSETTSConfig>;
  private audioPlayer: StreamingAudioPlayer;
  private currentStatus: TTSStatus = 'idle';
  private isDestroyed: boolean = false;
  private statusChangeCallbacks: ((status: TTSStatus) => void)[] = [];
  private playStartCallbacks: (() => void)[] = [];
  private playEndCallbacks: (() => void)[] = [];
  private currentAbortController: AbortController | null = null;
  private shouldStopRetry = false; // 标志是否应该停止重试

  constructor(config: HttpSSETTSConfig, eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({
      prefix: 'HttpSSETTSService',
    });

    // 设置默认配置
    this.config = {
      serverUrl: config.serverUrl,
      requestTimeout: config.requestTimeout || 30000,
      maxRetryAttempts: config.maxRetryAttempts || 3,
      retryDelay: config.retryDelay || 1000,
      seed: config.seed || 42,
      debug: config.debug || false,
    };

    // 创建音频播放器
    this.audioPlayer = new StreamingAudioPlayer({
      eventBus: eventBus,
      debug: this.config.debug,
    });

    this.logger.info('HTTP+SSE TTS服务初始化完成', {
      serverUrl: this.config.serverUrl,
      seed: this.config.seed,
    });

    this.bindEvents();
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 监听播放器状态变化
    this.eventBus.on('tts:player-state-change', (data: unknown) => {
      if (typeof data === 'object' && data !== null && 'state' in data) {
        const eventData = data as { state: string };
        this.handlePlayerStateChange(eventData.state as PlayerState);
      }
    });

    // 监听播放开始
    this.eventBus.on('tts:play-start', () => {
      this.playStartCallbacks.forEach(callback => callback());
      // 轻量化TTS事件：发送DOM事件给数字人组件
      this.dispatchTTSEvent('tts:play-start');
    });

    // 监听播放结束
    this.eventBus.on('tts:play-end', () => {
      this.playEndCallbacks.forEach(callback => callback());
      // 轻量化TTS事件：发送DOM事件给数字人组件
      this.dispatchTTSEvent('tts:play-end');
    });
  }

  /**
   * 语音合成
   */
  public async speak(text: string, instructText?: string): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('TTS服务已销毁');
    }

    try {
      this.logger.info('开始HTTP+SSE语音合成', {
        textLength: text.length,
        instructText,
        currentStatus: this.currentStatus,
      });

      // 只在必要时停止当前播放（避免不必要的中断）
      if (this.currentStatus !== 'idle' && this.currentStatus !== 'error') {
        this.logger.info('检测到正在进行的TTS操作，停止当前播放');
        this.stop();
      } else {
        this.logger.info('当前TTS状态为空闲，直接开始新的合成');
        // 重置停止重试标志（开始新的语音合成）
        this.shouldStopRetry = false;
      }

      // 设置状态为连接中
      this.setStatus('connecting');

      // 执行语音合成（带重试机制）
      await this.synthesizeWithRetry(text, instructText || '用自然清晰的普通话说话');
    } catch (error) {
      this.logger.error('语音合成失败', { error, text: text.substring(0, 50) });
      this.setStatus('error');
      throw error;
    }
  }

  /**
   * 带重试机制的语音合成
   */
  private async synthesizeWithRetry(text: string, instructText: string): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.config.maxRetryAttempts; attempt++) {
      // 检查是否应该停止重试
      if (this.shouldStopRetry) {
        this.logger.info('检测到停止重试标志，终止语音合成重试');
        return;
      }

      try {
        this.logger.info(`语音合成尝试 ${attempt}/${this.config.maxRetryAttempts}`);

        await this.performSynthesis(text, instructText);
        return; // 成功则直接返回
      } catch (error) {
        lastError = error as Error;
        this.logger.warn(`语音合成尝试 ${attempt} 失败`, { error: lastError.message });

        // 如果不是最后一次尝试，等待后重试
        if (attempt < this.config.maxRetryAttempts) {
          const delay = this.config.retryDelay * attempt; // 递增延迟
          this.logger.info(`等待 ${delay}ms 后重试`);

          // 在等待期间也要检查停止标志
          await new Promise(resolve => {
            const timeoutId = setTimeout(resolve, delay);

            // 创建一个检查停止标志的间隔
            const checkInterval = setInterval(() => {
              if (this.shouldStopRetry) {
                clearTimeout(timeoutId);
                clearInterval(checkInterval);
                this.logger.info('在重试等待期间检测到停止标志，提前终止等待');
                resolve(undefined);
              }
            }, 100); // 每100ms检查一次

            // 当定时器完成时清理间隔
            setTimeout(() => {
              clearInterval(checkInterval);
            }, delay);
          });

          // 再次检查停止标志
          if (this.shouldStopRetry) {
            this.logger.info('重试等待完成后检测到停止标志，终止语音合成重试');
            return;
          }
        }
      }
    }

    // 所有重试都失败
    throw new Error(
      `语音合成失败，已重试 ${this.config.maxRetryAttempts} 次: ${lastError?.message}`
    );
  }

  /**
   * 执行语音合成
   */
  private async performSynthesis(text: string, instructText: string): Promise<void> {
    // 创建新的AbortController
    this.currentAbortController = new AbortController();

    // 准备请求数据
    const formData = new FormData();
    formData.append('tts_text', text);
    formData.append('instruct_text', instructText);
    formData.append('seed', this.config.seed.toString());

    // 构建请求URL
    const url = `${this.config.serverUrl}/inference_instruct2_sse`;

    this.logger.info('发送HTTP+SSE请求', { url, instructText });

    // 发送HTTP请求
    const response = await fetch(url, {
      method: 'POST',
      body: formData,
      headers: {
        Accept: 'text/event-stream',
      },
      signal: this.currentAbortController.signal,
    });

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
    }

    if (!response.body) {
      throw new Error('响应体为空');
    }

    // 设置状态为加载中
    this.setStatus('loading');
    this.audioPlayer.startLoading();

    // 处理SSE流
    await this.processSSEStream(response.body);
  }

  /**
   * 处理SSE事件流
   */
  private async processSSEStream(body: ReadableStream<Uint8Array>): Promise<void> {
    const reader = body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    const audioChunks: AudioChunkData[] = [];

    try {
      // eslint-disable-next-line no-constant-condition
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });

        // 处理完整的SSE事件
        while (buffer.includes('\n\n')) {
          const eventEnd = buffer.indexOf('\n\n');
          const eventData = buffer.slice(0, eventEnd);
          buffer = buffer.slice(eventEnd + 2);

          if (eventData.trim()) {
            const event = this.parseSSEEvent(eventData);
            await this.handleSSEEvent(event, audioChunks);
          }
        }
      }

      this.logger.info(`SSE流处理完成，共接收 ${audioChunks.length} 个音频块`);
    } catch (error) {
      this.logger.error('SSE流处理失败', { error });
      throw error;
    } finally {
      reader.releaseLock();
    }
  }

  /**
   * 解析SSE事件
   */
  private parseSSEEvent(eventText: string): SSEEvent {
    const lines = eventText.split('\n');
    let type = 'message';
    let data = '';

    for (const line of lines) {
      if (line.startsWith('event: ')) {
        type = line.substring(7);
      } else if (line.startsWith('data: ')) {
        data = line.substring(6);
      }
    }

    return { type, data };
  }

  /**
   * 处理SSE事件
   */
  private async handleSSEEvent(event: SSEEvent, audioChunks: AudioChunkData[]): Promise<void> {
    this.logger.debug('收到SSE事件', { type: event.type });

    switch (event.type) {
      case 'start':
        this.logger.info('开始合成音频');
        break;

      case 'audio_chunk':
        try {
          const chunkData: AudioChunkData = JSON.parse(event.data);
          audioChunks.push(chunkData);

          this.logger.debug(`接收音频块 ${chunkData.chunk_id}`, {
            duration: chunkData.duration ? chunkData.duration.toFixed(3) : 'unknown',
            dataLength: chunkData.audio_data.length,
          });

          // 转换为AudioChunk格式并添加到播放器
          const audioChunk: AudioChunk = {
            chunkId: chunkData.chunk_id,
            isFinal: false, // 暂时设为false，在end事件中标记最后一个
            audioData: chunkData.audio_data,
            sampleRate: 24000, // 根据API文档设置默认采样率
            audioFormat: 'wav',
            mimeType: 'audio/wav',
          };

          this.audioPlayer.addAudioChunk(audioChunk);
        } catch (error) {
          this.logger.error('解析音频块失败', { error, data: event.data });
        }
        break;

      case 'end':
        try {
          const endData: SynthesisEndData = JSON.parse(event.data);

          // 构建日志信息，处理可选的total_duration字段
          const durationText = endData.total_duration
            ? `，总时长 ${endData.total_duration.toFixed(3)}秒`
            : '';

          this.logger.info(`合成完成！总计 ${endData.total_chunks} 个音频块${durationText}`, {
            message: endData.message,
            mode: endData.mode,
            total_chunks: endData.total_chunks,
            total_duration: endData.total_duration,
          });

          // 标记音频接收完成，不重复添加音频块
          this.audioPlayer.markReceivingComplete();
          this.logger.info('音频接收完成标记已设置', {
            totalChunks: endData.total_chunks,
            receivedChunks: audioChunks.length,
          });
        } catch (error) {
          this.logger.error('解析结束数据失败', { error, data: event.data });

          // 即使解析失败，也要标记接收完成
          this.audioPlayer.markReceivingComplete();
          this.logger.info('容错处理：音频接收完成标记已设置', {
            receivedChunks: audioChunks.length,
          });
        }
        break;

      default:
        this.logger.warn('未知SSE事件类型', { type: event.type });
    }
  }

  /**
   * 处理播放器状态变化
   */
  private handlePlayerStateChange(playerState: PlayerState): void {
    switch (playerState) {
      case PlayerState.PLAYING:
        this.setStatus('playing');
        break;
      case PlayerState.PAUSED:
        this.setStatus('paused');
        break;
      case PlayerState.ENDED:
      case PlayerState.IDLE:
        this.setStatus('idle');
        break;
      case PlayerState.ERROR:
        this.setStatus('error');
        break;
    }
  }

  /**
   * 设置TTS状态
   */
  private setStatus(status: TTSStatus): void {
    if (this.currentStatus === status) {
      return;
    }

    const oldStatus = this.currentStatus;
    this.currentStatus = status;

    this.logger.info('TTS状态变化', { from: oldStatus, to: status });

    // 通知状态变化回调
    this.statusChangeCallbacks.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        this.logger.error('状态变化回调执行失败', { error });
      }
    });

    // 发送状态变化事件
    this.eventBus.emit('tts:status-change', { status, previousStatus: oldStatus });
  }

  /**
   * 轻量化TTS事件：发送DOM事件给数字人组件
   */
  private dispatchTTSEvent(eventType: string): void {
    try {
      const event = new CustomEvent(eventType, {
        detail: { timestamp: Date.now(), service: 'HttpSSETTS' },
        bubbles: true,
      });
      document.dispatchEvent(event);
      this.logger.debug('发送TTS DOM事件', { eventType });
    } catch (error) {
      this.logger.warn('发送TTS DOM事件失败', { eventType, error });
    }
  }

  /**
   * 停止播放
   */
  public stop(): void {
    this.logger.info('停止TTS播放');

    // 设置停止重试标志
    this.shouldStopRetry = true;

    // 取消当前请求
    if (this.currentAbortController) {
      this.currentAbortController.abort();
      this.currentAbortController = null;
    }

    // 停止音频播放
    this.audioPlayer.stop();

    // 只在有活跃播放时才发送播放结束事件（避免不必要的事件）
    if (this.currentStatus !== 'idle' && this.currentStatus !== 'error') {
      this.logger.info('发送TTS播放结束事件');

      // 发送播放结束事件（确保数字人停止说话动画）
      this.playEndCallbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          this.logger.error('播放结束回调执行失败', { error });
        }
      });

      // 发送DOM事件给数字人组件
      this.dispatchTTSEvent('tts:play-end');

      // 发送事件总线事件
      this.eventBus.emit('tts:play-end');
    } else {
      this.logger.info('当前TTS状态为空闲，跳过播放结束事件');
    }

    this.setStatus('idle');
  }

  /**
   * 暂停播放
   */
  public pause(): void {
    this.logger.info('暂停TTS播放');
    this.audioPlayer.pause();
  }

  /**
   * 继续播放
   */
  public resume(): void {
    this.logger.info('继续TTS播放');
    this.audioPlayer.resume();
  }

  /**
   * 监听播放状态
   */
  public onStatusChange(callback: (status: TTSStatus) => void): void {
    this.statusChangeCallbacks.push(callback);
  }

  /**
   * 监听播放开始
   */
  public onPlayStart(callback: () => void): void {
    this.playStartCallbacks.push(callback);
  }

  /**
   * 监听播放结束
   */
  public onPlayEnd(callback: () => void): void {
    this.playEndCallbacks.push(callback);
  }

  /**
   * 获取当前状态
   */
  public getStatus(): TTSStatus {
    return this.currentStatus;
  }

  /**
   * 检查连接状态
   */
  public isConnected(): boolean {
    // HTTP+SSE模式下，没有持久连接概念
    // 只要服务未销毁且不在错误状态，就认为可用
    return !this.isDestroyed && this.currentStatus !== 'error';
  }

  /**
   * 销毁服务
   */
  public destroy(): void {
    if (this.isDestroyed) {
      return;
    }

    this.logger.info('销毁HTTP+SSE TTS服务');

    this.isDestroyed = true;

    // 停止当前播放
    this.stop();

    // 销毁音频播放器
    this.audioPlayer.destroy();

    // 清空回调
    this.statusChangeCallbacks = [];
    this.playStartCallbacks = [];
    this.playEndCallbacks = [];

    this.setStatus('idle');
  }
}
