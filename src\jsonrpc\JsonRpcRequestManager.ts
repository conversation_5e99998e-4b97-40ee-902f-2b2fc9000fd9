/**
 * JSON-RPC消息发送器
 * 提供Promise接口但不维护长期pending状态
 * 每个请求创建临时Promise，响应后立即清理
 */

import { EventBus } from '../core/EventBus';
import {
  JsonRpcMessage,
  JsonRpcMessageOptions,
  JsonRpcResponse,
  JsonRpcErrorResponse,
} from '../types/jsonrpc';
import { Logger } from '../utils/Logger';

import { JsonRpcValidator } from './JsonRpcValidator';

/**
 * 临时请求记录（仅用于Promise解析）
 */
interface TemporaryRequest {
  resolve: (result: unknown) => void;
  reject: (error: Error) => void;
  timeoutId: NodeJS.Timeout;
}

/**
 * JSON-RPC消息发送器
 * 提供Promise接口，但不维护长期pending状态
 */
export class JsonRpcRequestManager {
  private eventBus: EventBus;
  private logger: Logger;
  private validator: JsonRpcValidator;
  private temporaryRequests = new Map<string, TemporaryRequest>();

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = Logger.getInstance({ prefix: 'JsonRpcRequestManager' });
    this.validator = new JsonRpcValidator();
    this.setupEventListeners();
  }

  /**
   * 发送JSON-RPC消息
   * 返回Promise但不维护长期pending状态
   */
  public async sendJsonRpcMessage(
    message: JsonRpcMessage,
    options: JsonRpcMessageOptions = {}
  ): Promise<unknown> {
    // 基础格式验证
    if (!this.validator.validateRequest(message)) {
      throw new Error('消息格式不符合JSON-RPC 2.0规范');
    }

    // 验证方法名
    if (!this.validator.validateMethodName(message.method)) {
      throw new Error(`不支持的方法名: ${message.method}`);
    }

    // 验证参数格式
    if (!this.validator.validateMethodParams(message.method, message.params)) {
      throw new Error(`方法 ${message.method} 的参数格式不正确`);
    }

    const requestId = String(message.id);
    const { timeout = 30000 } = options;

    this.logger.info('📤 发送JSON-RPC请求', {
      method: message.method,
      id: requestId,
      hasParams: message.params !== undefined,
    });

    // 创建临时Promise用于等待响应
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        this.temporaryRequests.delete(requestId);
        reject(new Error(`请求超时: ${requestId}`));
      }, timeout);

      // 临时存储Promise解析器
      this.temporaryRequests.set(requestId, {
        resolve,
        reject,
        timeoutId,
      });

      // 发送请求事件 - 由JsonRpcBusinessHandler处理
      this.eventBus.emit('client:send-request', message);
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听JSON-RPC响应
    this.eventBus.on('jsonrpc:response', (response: unknown) => {
      if (typeof response === 'object' && response !== null) {
        this.handleResponse(response as JsonRpcResponse);
      }
    });

    // 监听JSON-RPC错误响应
    this.eventBus.on('jsonrpc:error-response', (response: unknown) => {
      if (typeof response === 'object' && response !== null) {
        this.handleErrorResponse(response as JsonRpcErrorResponse);
      }
    });
  }

  /**
   * 处理成功响应
   */
  private handleResponse(response: JsonRpcResponse): void {
    const requestId = String(response.id);
    const tempRequest = this.temporaryRequests.get(requestId);

    if (!tempRequest) {
      // 这是正常的，因为客户端可能不等待某些请求的响应
      return;
    }

    this.logger.info('✅ 收到JSON-RPC响应', { requestId });

    // 清理超时定时器
    clearTimeout(tempRequest.timeoutId);

    // 移除临时请求
    this.temporaryRequests.delete(requestId);

    // 解析Promise
    tempRequest.resolve(response.result);
  }

  /**
   * 处理错误响应
   */
  private handleErrorResponse(response: JsonRpcErrorResponse): void {
    const requestId = String(response.id);
    const tempRequest = this.temporaryRequests.get(requestId);

    if (!tempRequest) {
      // 这是正常的，因为客户端可能不等待某些请求的响应
      return;
    }

    this.logger.error('❌ 收到JSON-RPC错误响应', {
      requestId,
      errorCode: response.error?.code,
      errorMessage: response.error?.message,
    });

    // 清理超时定时器
    clearTimeout(tempRequest.timeoutId);

    // 移除临时请求
    this.temporaryRequests.delete(requestId);

    // 创建错误对象
    const error = new Error(
      `JSON-RPC错误 [${response.error.code}]: ${response.error.message}`
    ) as Error & {
      code?: number;
      data?: unknown;
    };
    error.code = response.error.code;
    error.data = response.error.data;

    // 拒绝Promise
    tempRequest.reject(error);
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.logger.info('🗑️ 销毁JSON-RPC消息发送器');

    // 清理所有临时请求
    for (const [requestId, tempRequest] of this.temporaryRequests) {
      clearTimeout(tempRequest.timeoutId);
      tempRequest.reject(new Error(`SDK正在销毁: ${requestId}`));
    }
    this.temporaryRequests.clear();

    this.logger.info('✅ JSON-RPC消息发送器已销毁');
  }
}
